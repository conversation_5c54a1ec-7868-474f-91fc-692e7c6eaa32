import 'package:flutter/material.dart';
import '../theme/app_colors.dart';
import 'homepage.dart';
import 'categories_page.dart';
import 'cart_page.dart';
import 'favorites_page.dart';
import 'profile_page.dart';

/// MainNavigation - The main navigation wrapper with bottom navigation bar
///
/// This widget provides:
/// - Modern bottom navigation bar with 5 essential tabs
/// - Smooth navigation between different screens
/// - Badge support for cart items and notifications
/// - Professional design matching the app theme
/// - User-friendly navigation experience
class MainNavigation extends StatefulWidget {
  const MainNavigation({super.key});

  @override
  State<MainNavigation> createState() => _MainNavigationState();
}

class _MainNavigationState extends State<MainNavigation> {
  int _currentIndex = 0;
  
  // Sample cart count for badge display
  int _cartItemCount = 3;
  
  // Sample favorites count
  int _favoritesCount = 5;

  // List of pages for navigation
  late final List<Widget> _pages;

  @override
  void initState() {
    super.initState();
    _pages = [
      const HomePage(),
      const CategoriesPage(),
      const CartPage(),
      const FavoritesPage(),
      const ProfilePage(),
    ];
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: _buildBottomNavigationBar(),
    );
  }

  /// Builds the modern bottom navigation bar
  Widget _buildBottomNavigationBar() {
    return Container(
      decoration: const BoxDecoration(
        color: AppColors.surface,
        boxShadow: [
          BoxShadow(
            color: AppColors.shadowMedium,
            offset: Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 70,
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              _buildNavItem(
                index: 0,
                icon: Icons.home_outlined,
                activeIcon: Icons.home,
                label: 'Home',
              ),
              _buildNavItem(
                index: 1,
                icon: Icons.category_outlined,
                activeIcon: Icons.category,
                label: 'Categories',
              ),
              _buildNavItem(
                index: 2,
                icon: Icons.shopping_cart_outlined,
                activeIcon: Icons.shopping_cart,
                label: 'Cart',
                badgeCount: _cartItemCount,
              ),
              _buildNavItem(
                index: 3,
                icon: Icons.favorite_outline,
                activeIcon: Icons.favorite,
                label: 'Favorites',
                badgeCount: _favoritesCount,
              ),
              _buildNavItem(
                index: 4,
                icon: Icons.person_outline,
                activeIcon: Icons.person,
                label: 'Profile',
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Builds individual navigation item with badge support
  Widget _buildNavItem({
    required int index,
    required IconData icon,
    required IconData activeIcon,
    required String label,
    int? badgeCount,
  }) {
    final isActive = _currentIndex == index;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _currentIndex = index;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: isActive 
              ? AppColors.primary.withOpacity(0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Icon with badge
            Stack(
              clipBehavior: Clip.none,
              children: [
                Icon(
                  isActive ? activeIcon : icon,
                  color: isActive 
                      ? AppColors.primary 
                      : AppColors.textSecondary,
                  size: 24,
                ),
                
                // Badge for cart and favorites
                if (badgeCount != null && badgeCount > 0)
                  Positioned(
                    right: -6,
                    top: -6,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: const BoxDecoration(
                        color: AppColors.error,
                        shape: BoxShape.circle,
                      ),
                      constraints: const BoxConstraints(
                        minWidth: 16,
                        minHeight: 16,
                      ),
                      child: Text(
                        badgeCount > 99 ? '99+' : badgeCount.toString(),
                        style: const TextStyle(
                          color: AppColors.textOnPrimary,
                          fontSize: 10,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 4),
            
            // Label
            Text(
              label,
              style: TextStyle(
                color: isActive 
                    ? AppColors.primary 
                    : AppColors.textSecondary,
                fontSize: 11,
                fontWeight: isActive 
                    ? FontWeight.w600 
                    : FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Updates cart item count (to be called from other screens)
  void updateCartCount(int count) {
    setState(() {
      _cartItemCount = count;
    });
  }

  /// Updates favorites count (to be called from other screens)
  void updateFavoritesCount(int count) {
    setState(() {
      _favoritesCount = count;
    });
  }
}
